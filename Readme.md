# 健康记录CSV自动导入系统

一个基于Python的健康记录数据自动导入系统，支持从SFTP服务器自动下载CSV文件并导入到MySQL数据库，具备定时调度和原子化数据更新功能。

## 🚀 功能特性

- ✅ **SFTP自动下载**：自动从远程SFTP服务器下载health_record__开头的CSV文件
- ✅ **定时任务调度**：使用APScheduler实现可配置的定时执行（默认每小时执行一次）
- ✅ **原子化数据更新**：使用临时表机制确保数据导入的原子性和一致性
- ✅ **特殊格式支持**：处理使用\u001E分隔符的CSV文件
- ✅ **批量处理**：支持大文件的分批处理，提高性能
- ✅ **完善的日志系统**：文件和控制台双重日志输出，支持不同日志级别
- ✅ **环境变量配置**：灵活的配置管理
- ✅ **Docker容器化**：提供完整的Docker部署方案
- ✅ **错误恢复**：完善的异常处理和资源清理机制

## 📋 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   SFTP Server   │───▶│  CSV Processor  │───▶│  MySQL Database │
│                 │    │                 │    │                 │
│ health_record__ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ health_record.ok│    │ │ Temp Table  │ │    │ │ health_record│ │
└─────────────────┘    │ │ Processing  │ │    │ │   (Final)   │ │
                       │ └─────────────┘ │    │ └─────────────┘ │
                       └─────────────────┘    └─────────────────┘
```

## ⚙️ 环境配置

创建 `.env` 文件并配置以下参数：

```bash
# 数据库配置
DATABASE_URL=mysql+pymysql://username:password@host:port/database
DB_TABLE=health_record

# SFTP服务器配置
SFTP_HOST=your.sftp.server
SFTP_PORT=22
SFTP_USERNAME=your_username
SFTP_PASSWORD=your_password
SFTP_REMOTE_PATH=/path/to/remote/directory

# 本地配置
DATA_DIR=./data
IMPORT_DIR=./data
BATCH_SIZE=1000

# 调度配置
SCHEDULE_HOUR=1  # 每隔1小时执行一次

# 日志配置
LOG_LEVEL=INFO   # DEBUG, INFO, WARNING, ERROR
```

## 🗃️ 数据模型

系统处理健康记录数据，包含以下字段：

| 字段名 | 类型 | 描述 | 备注 |
|--------|------|------|------|
| center_id | VARCHAR(255) | 中心ID | 主键 |
| cust_name | VARCHAR(255) | 客户姓名 | |
| cust_birth | DATE | 出生日期 | |
| cust_gender_nm | VARCHAR(50) | 性别 | |
| age | INT | 年龄 | |
| height_sf | TEXT | 身高信息 | |
| weight_sf | TEXT | 体重信息 | |
| diagnosis | MEDIUMTEXT | 诊断信息 | |
| emr | MEDIUMTEXT | 电子病历 | |
| drug_plan_detail | MEDIUMTEXT | 药物计划详情 | |
| allergy_history | TEXT | 过敏史 | |
| family_history | TEXT | 家族病史 | |
| labtest_report | MEDIUMTEXT | 实验室报告 | |
| medicalexam | MEDIUMTEXT | 体检结果 | |
| risk_flag | TEXT | 风险标志 | |
| jbs | MEDIUMTEXT | JBS评分 | |

### 数据库表结构

```sql
CREATE TABLE IF NOT EXISTS health_record (
    center_id VARCHAR(255) PRIMARY KEY,
    cust_name VARCHAR(255),
    cust_birth DATE,
    cust_gender_nm VARCHAR(50),
    age INT,
    height_sf TEXT,
    weight_sf TEXT,
    diagnosis MEDIUMTEXT,
    emr MEDIUMTEXT,
    drug_plan_detail MEDIUMTEXT,
    allergy_history TEXT,
    family_history TEXT,
    labtest_report MEDIUMTEXT,
    medicalexam MEDIUMTEXT,
    risk_flag TEXT,
    jbs MEDIUMTEXT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 🚀 快速开始

### 1. 本地运行

```bash
# 1. 克隆项目
git clone <repository-url>
cd csv_import_py

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置环境变量
cp env_release .env
# 编辑 .env 文件，配置你的数据库和SFTP信息

# 4. 运行程序
python main.py
```

### 2. Docker部署

```bash
# 构建镜像
docker build -t csv_importer:latest .

# 运行容器
docker run -d \
  --name csv_importer \
  -v $(pwd)/.env:/app/.env \
  -v $(pwd)/logs:/app/logs \
  csv_importer:latest
```

### 3. Podman部署

```bash
# 构建多架构镜像
podman build --platform=linux/amd64 --format=docker -t csv_importer:3.11-amd64 .

# 运行容器
podman run -d \
  --name csv_importer \
  -v $(pwd)/.env:/app/.env \
  -v $(pwd)/logs:/app/logs \
  csv_importer:3.11-amd64
```

## 📝 工作流程

1. **初始化**：程序启动时立即执行一次数据导入
2. **SFTP检查**：连接SFTP服务器，检查是否存在 `health_record.ok` 文件
3. **文件下载**：下载所有 `health_record__` 开头的CSV文件到本地
4. **数据处理**：
   - 创建临时表 `health_record_temp`
   - 解析CSV文件（使用 `\u001E` 分隔符）
   - 批量导入数据到临时表
5. **原子替换**：将临时表重命名为正式表，确保数据一致性
6. **清理工作**：删除远程文件，清理本地临时文件
7. **定时重复**：按配置的时间间隔重复执行

## 📊 日志监控

日志文件位置：`./logs/import.log`

日志级别：
- `DEBUG`：详细的调试信息
- `INFO`：一般信息（默认）
- `WARNING`：警告信息
- `ERROR`：错误信息

查看实时日志：
```bash
tail -f logs/import.log
```

## 🔧 开发和部署

### 项目打包

```bash
tar czvf release.tar.gz \
    --exclude='.git' \
    --exclude='.env' \
    --exclude='data/*' \
    --exclude='logs' \
    --exclude='.aider*' \
    --exclude='*.pyc' \
    --exclude='__pycache__' \
    --exclude='._*' \
    --exclude='.DS_Store' \
    --exclude='.gitignore' \
    .
```

### 依赖管理

主要依赖：
- `pandas==2.2.3` - 数据处理
- `SQLAlchemy==2.0.0` - 数据库ORM
- `APScheduler==3.10.0` - 定时任务调度
- `python-dotenv==0.19.0` - 环境变量管理
- `PyMySQL>=1.1.0` - MySQL数据库驱动
- `paramiko>=3.4.0` - SFTP客户端

## ⚠️ 注意事项

1. **CSV格式**：文件必须使用 `\u001E` 作为字段分隔符
2. **文件命名**：只处理以 `health_record__` 开头的文件
3. **确认文件**：必须存在 `health_record.ok` 文件才会开始处理
4. **数据安全**：使用临时表机制确保数据导入的原子性
5. **资源清理**：处理完成后会自动删除远程文件
6. **权限要求**：确保数据库用户有创建表、删除表的权限

## 🐛 故障排除

### 常见问题

1. **SFTP连接失败**
   - 检查网络连接和防火墙设置
   - 验证SFTP服务器地址、端口、用户名和密码

2. **数据库连接失败**
   - 检查数据库服务器状态
   - 验证数据库连接字符串格式
   - 确认用户权限

3. **CSV解析错误**
   - 检查文件编码（应为UTF-8）
   - 验证分隔符是否为 `\u001E`
   - 检查文件格式是否完整

4. **内存不足**
   - 调整 `BATCH_SIZE` 参数
   - 增加系统内存或使用更大的容器

## 📄 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。